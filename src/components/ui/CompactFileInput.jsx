import { useEffect, useState } from 'react';
import { useField } from 'formik';
import { useDropzone } from 'react-dropzone';
import { X, Upload, Image } from 'lucide-react';

const assetBaseURL = import.meta.env.VITE_ASSET_HOST_URL || '';

export const CompactFileInput = ({ 
  label, 
  accept = "image/*", 
  placeholder = "Upload Image", 
  required = false, 
  ...props 
}) => {
  const [field, meta, helpers] = useField(props);
  const [preview, setPreview] = useState(null);
  const [formatError, setFormatError] = useState('');

  const onDrop = (acceptedFiles) => {
    if (!acceptedFiles.length) return;
    const file = acceptedFiles[0];

    // Basic image validation
    if (!file.type.startsWith('image/')) {
      setFormatError('Only image files are allowed.');
      helpers.setValue(null);
      setPreview(null);
      return;
    }

    setFormatError('');
    helpers.setValue(file);
  };

  const { getRootProps, getInputProps, isDragActive } = useDropzone({
    onDrop,
    multiple: false,
    accept: { 'image/*': ['.jpg', '.jpeg', '.png', '.gif', '.webp'] }
  });

  const removeFile = () => {
    helpers.setValue(null);
    setPreview(null);
    setFormatError('');
  };

  useEffect(() => {
    if (typeof field.value === 'string' && field.value !== '') {
      // Handle existing file from server
      setPreview(`${assetBaseURL}${field.value}`);
    } else if (field.value instanceof File) {
      // Handle newly selected file
      const objectUrl = URL.createObjectURL(field.value);
      setPreview(objectUrl);
      return () => URL.revokeObjectURL(objectUrl);
    } else {
      setPreview(null);
    }
  }, [field.value]);

  return (
    <div className="w-full">
      {label && (
        <label className="block text-sm font-medium text-gray-700 dark:text-gray-200 mb-2">
          {label} {required && <span className="text-red-500">*</span>}
        </label>
      )}
      
      <div className="relative">
        {!preview ? (
          <div
            {...getRootProps()}
            className={`h-20 flex items-center justify-center border-2 border-dashed rounded-lg cursor-pointer transition-colors
              ${isDragActive 
                ? 'border-blue-500 bg-blue-50 dark:bg-blue-900/20' 
                : 'border-gray-300 dark:border-gray-600 hover:border-gray-400'
              }`}
          >
            <input {...getInputProps()} />
            <div className="flex items-center space-x-2 text-gray-500 dark:text-gray-400">
              <Upload size={16} />
              <span className="text-sm">
                {isDragActive ? 'Drop here...' : placeholder}
              </span>
            </div>
          </div>
        ) : (
          <div className="relative h-20 bg-gray-100 dark:bg-gray-800 rounded-lg overflow-hidden">
            <img 
              src={preview} 
              alt="Preview" 
              className="w-full h-full object-cover"
            />
            <button
              type="button"
              onClick={removeFile}
              className="absolute top-1 right-1 bg-white dark:bg-gray-700 rounded-full p-1 text-red-600 shadow-sm hover:bg-red-50"
            >
              <X size={14} />
            </button>
            <div className="absolute bottom-1 left-1 bg-black bg-opacity-50 text-white text-xs px-2 py-1 rounded">
              <Image size={12} className="inline mr-1" />
              {field.value instanceof File ? field.value.name : 'Image'}
            </div>
          </div>
        )}
      </div>

      {(meta.touched && meta.error) || formatError ? (
        <div className="text-red-600 dark:text-red-400 text-xs mt-1">
          {meta.error || formatError}
        </div>
      ) : null}
    </div>
  );
};

export default CompactFileInput;
