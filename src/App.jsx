import { lazy, Suspense } from "react";
import { Navigate, Route, Routes } from "react-router-dom";

import PrivateOutlet from "./routes/PrivateOutlet";
import PublicOutlet from "./routes/PublicOutlet";

// home pages  & dashboard
//import Dashboard from "./pages/dashboard";
import Profile from "./pages/Profile";
import Dashboard from "./pages/dashboard";
import ChangeLog from "./pages/changelog";
import MenuSettings from "./pages/menu-settings";
import CategoryDetails from "./pages/menu-settings/Details";
import MentorList from "./pages/MentorList";
import MentorDetails from "./pages/MentorList/detailsMentor";
import StudentList from "./pages/StudentList";
import StudentDetails from "./pages/StudentList/detailsStudent";
import MasterTagsList from "./pages/MasterSettings/TagsList";
import MasterOrganizationList from "./pages/MasterSettings/OrganizationList";
import ConfigureOrganization from "./pages/MasterSettings/Organization/Configure";

import MasterUserList from "./pages/MasterSettings/UserList";
import RawContentClassList from "./pages/RawContent/ClassList";
import RawContentSubjectList from "./pages/RawContent/SubjectList";
import RawContentChapterList from "./pages/RawContent/ChapterList";
import RawContentVideoContentList from "./pages/RawContent/VideoContentList";
import RawContentScriptList from "./pages/RawContent/ScriptList";
import RawContentQuizList from "./pages/RawContent/QuizList";
import QuestionList from "./pages/RawContent/QuizList/Question";
import QuizSubjectList from "./pages/RawContent/QuizList/Subject";
import WrittenQuestion from "./pages/RawContent/QuizList/Written";
import CourseSetupCourseList from "./pages/CourseSetup/Course";
import CourseCreate from "./pages/CourseSetup/Course/Create";
// import  CourseDetails from "./pages/CourseSetup/CourseList/CourseDetails";
import CourseDetails from "./pages/CourseSetup/Course/Details";
import CourseOldDetails from "./pages/CourseSetup/Course/OldDetails";
import CourseCategoryList from "./pages/CourseSetup/Course/CourseCategory";
import CourseSetupCourseListOutline from "./pages/CourseSetup/Course/Outline";
import CourseSetupCourseListFAQ from "./pages/CourseSetup/Course/FAQ";
import CourseSetupCourseListFeature from "./pages/CourseSetup/Course/Feature";
import CourseSetupCourseListRoutine from "./pages/CourseSetup/Course/Routine";
import CourseMentorAssign from "./pages/CourseSetup/Course/MentorAssign";
import CourseSetupCourseListParticipant from "./pages/CourseSetup/Course/Participants";
import CourseSetupCourseListMentorAssign from "./pages/CourseSetup/Course/MentorAssign";
import CourseSetupStudentMappingList from "./pages/CourseSetup/StudentMapping";
import ContentSetup from "./pages/ContentSetup/Content";
import ContentSubject from "./pages/ContentSetup/Subject";
// import ContentSubject from "./pages/ContentSetup/Content/Subject";
import EnrollmentList from "./pages/EnrollmentList";
import PendingPaymentList from "./pages/EnrollmentList/PendingList";
import CreateEnrollment from "./pages/EnrollmentList/Create";
import CompletedClassList from "./pages/CompletedClassList";
import AssignmentList from "./pages/AssignmentList";
import CreateAssignmentPage from "./pages/AssignmentList/CreateAssignmentPage";
import ExamResultList from "./pages/ExamResultList";
import PaymentList from "./pages/PaymentList";
import PaymentCollection from "./pages/PaymentList/Collection";
import PurchaseList from "./pages/PurchaseList";
import AllPaymentsList from "./pages/AllPaymentsList";
import InvoicePage from "./pages/AllPaymentsList/InvoicePage";
import Login from "./pages/auth/login";
import Register from "./pages/auth/register";
import ForgotPass from "./pages/auth/forgot-password";
import LockScreen from "./pages/auth/lock-screen";
import Error from "./pages/404";
import TryNewLMS from "./pages/auth/TryNewLMS"; // TryNewLMS import("./pages/auth/TryNewLMS"));
import AuthLayout from "./layout/AuthLayout";
import Layout from "./layout/Layout";
import Loading from "@/components/Loading";
import PromotionalLayout from "./layout/PromotionalLayout";
import Home from "./pages/Promotional/Home";
import StudentDashboard from "./pages/dashboard/student-dashboard";
import MentorDashboard from "./pages/dashboard/mentor-dashboard";
import Price from "./pages/Promotional/Price";
import UpdateOrganization from "./pages/MasterSettings/Organization/UpdateOrganization";
import ResoursesPage from "./pages/Promotional/Components/ResoursesPage/ResoursesPage";
import Demo from "./pages/Promotional/Demo";
import OfflineExamResult from "./pages/OfflineExamResult";
import OfflineExamDetails from "./pages/OfflineExamResult/OfflineExamDetails";
import Batch from "./pages/CourseSetup/Course/Batch/Index";
import BatchList from "./pages/BatchList/BatchList";
import AddBatch from "./pages/BatchList/AddBatch";
import BatchDetails from "./pages/BatchList/BatchDetails";
import Attendance from "./pages/Attendance/Routine";
import ClassList from "./pages/ClassList";
import Announcement from "./pages/Announcement";
import Coupon from "./pages/Coupon";
import PaymentGateway from "./pages/MasterSettings/PaymentGateway";
import TypeList from "./pages/PaymentSettings/Settings/TypeList";
import OnlineExamDetails from "./pages/ExamResultList/OnlineExamDetails";
import { useSelector } from "react-redux";



import MasterDashboard from "./pages/MasterDashborad";
import SamplePage from "./SuperAdmin";
import Organizations from "./SuperAdmin/Organizations";
import OrganizationDetails from "./SuperAdmin/Organizations/details";
import PricingPlan from "./SuperAdmin/PricingPlan";
import PricingPlanDetails from "./SuperAdmin/PricingPlan/Details";
import PricingPlanCreate from "./SuperAdmin/PricingPlan/Create";
import Testimonials from "./pages/Testimonials/Testimonials";
import PromotionalItems from "./pages/PromotionalItems/PromotionalItems";
import EbookList from "./pages/Ebooks";
import Categories from "./pages/Categories/Categories";
import DiscussionList from "./pages/Discussions";
import DiscussionDetails from "./pages/Discussions/DiscussionDetails";
import Language from "./pages/MasterSettings/Language";
import Registration from "./pages/Registration";





function App() {

  const { user } = useSelector((state) => state.auth);


  return (
    <main className="App  relative">
      <Routes>
        <Route path="/" element={<PromotionalLayout />}>
          <Route path="/" element={<Home />} />
          <Route path="/pricing" element={<Price />} />
          <Route path="/demo" element={<Demo />} />
          <Route path="/resources" element={<ResoursesPage />} />
          <Route path="/try-new-lms" element={<TryNewLMS />} />
          <Route path="/registration" element={<Registration />} />
        </Route>
        <Route element={<PublicOutlet />}>
          <Route path="/" element={<AuthLayout />}>
            <Route path="/login" element={<Login />} />
            <Route path="/register" element={<Register />} />
            <Route path="/forgot-password" element={<ForgotPass />} />
            <Route path="/lock-screen" element={<LockScreen />} />
          </Route>
        </Route>

        <Route element={<PrivateOutlet />}>
          <Route path="/*" element={<Layout />}>


            <Route path="profile" element={<Profile />} />
            <Route path="dashboard" element={user?.organization_id ? <Dashboard /> : <MasterDashboard />} />
            <Route path="changelog" element={<ChangeLog />} />
            <Route path="course-category" element={<MenuSettings />} />
            <Route path="course-category-details/:id" element={<CategoryDetails />} />

            {/* Mentor route start */}
            <Route path="mentor-list" element={<MentorList />} />
            <Route path="mentor-details/:id" element={<MentorDetails />} />
            <Route path="mentor-dashboard/:id" element={<MentorDashboard />} />
            {/* Mentor route end */}

            {/* Student route start */}

            <Route path="student-list" element={<StudentList />} />
            <Route path="student-details/:id" element={<StudentDetails />} />
            <Route
              path="student-dashboard/:id"
              element={<StudentDashboard />}
            />

            <Route path="class-list" element={<ClassList />} />

            {/* Master Setting start */}
            <Route
              path="organization-list"
              element={<MasterOrganizationList />}
            />

            <Route
              path="configure-organization-old"
              element={<ConfigureOrganization />}
            />
            <Route path="configure-organization" element={<UpdateOrganization />} />
            <Route path="translations" element={<Language />} />
            <Route path="master-setting-tags" element={<MasterTagsList />} />
            <Route path="user-list" element={<MasterUserList />} />
            {/* Master Setting end */}

            {/* Raw Content Route start */}
            {/* <Route path="class-list" element={<RawContentClassList />} /> */}
            <Route
              path="raw-content-subjectList"
              element={<RawContentSubjectList />}
            />
            <Route
              path="raw-content-chapterList"
              element={<RawContentChapterList />}
            />
            <Route path="video-list" element={<RawContentVideoContentList />} />
            <Route
              path="raw-content-scriptList"
              element={<RawContentScriptList />}
            />
            <Route
              path="raw-content-quizList"
              element={<RawContentQuizList />}
            />
            <Route path="quiz-details/:id" element={<QuestionList />} />
            <Route path="quiz-subject-list/:id" element={<QuizSubjectList />} />
            <Route
              path="quiz-written-question/:id"
              element={<WrittenQuestion />}
            />
            {/* Raw Content Route end */}

            {/* Course Setup Route Start */}
            <Route path="course-list" element={<CourseSetupCourseList />} />
            <Route path="course-create" element={<CourseCreate />} />
            <Route path="course-create/:id" element={<CourseCreate />} />
            <Route
              path="old-course-details/:id"
              element={<CourseOldDetails />}
            />
            <Route path="course-details/:id" element={<CourseDetails />} />
            {/* <Route course-details/{id}
              path="/course-details/:courseId"
              element={<CourseDetails course={course} />}
            /> */}
            <Route path="category-list/:id" element={<CourseCategoryList />} />
            <Route
              path="courseSetup-courseList-outline/:id"
              element={<CourseSetupCourseListOutline />}
            />
            <Route
              path="courseSetup-courseList-faq/:id"
              element={<CourseSetupCourseListFAQ />}
            />
            <Route
              path="courseSetup-courseList-feature/:id"
              element={<CourseSetupCourseListFeature />}
            />
            <Route
              path="courseSetup-courseList-routine/:id"
              element={<CourseSetupCourseListRoutine />}
            />
            <Route path="mentor-assign/:id" element={<CourseMentorAssign />} />
            <Route
              path="courseSetup-courseList-participant/:id"
              element={<CourseSetupCourseListParticipant />}
            />
            <Route
              path="student-mapping-list"
              element={<CourseSetupStudentMappingList />}
            />
            {/* Course Setup Route end */}

            {/* Content Setup start */}
            <Route path="content-setup" element={<ContentSetup />} />
            <Route path="content-subject/:id" element={<ContentSubject />} />
            {/* Content Setup end */}

            <Route path="enrollment-list" element={<EnrollmentList />} />
            <Route path="enroll" element={<CreateEnrollment />} />
            <Route
              path="completed-class-list"
              element={<CompletedClassList />}
            />
            <Route path="assignment-list" element={<AssignmentList />} />
            <Route path="create-assignment" element={<CreateAssignmentPage />} />
            <Route path="exam-result-list" element={<ExamResultList />} />
            <Route path="exam-result-details/:id" element={<OnlineExamDetails />} />
            <Route
              path="offline-exam-results"
              element={<OfflineExamResult />}
            />
            <Route
              path="offline-exam-details/:id"
              element={<OfflineExamDetails />}
            />

            <Route path="payment-settings" element={<TypeList />} />

            <Route path="pending-payment-list" element={<PendingPaymentList />} />
            <Route path="payment-list" element={<PaymentList />} />
            <Route path="all-payments" element={<AllPaymentsList />} />
            <Route path="invoice/:id" element={<InvoicePage />} />
            <Route path="purchase-list" element={<PurchaseList />} />
            <Route path="batch-list" element={<BatchList />} />
            <Route path="announcement" element={<Announcement />} />
            <Route path="coupons" element={<Coupon />} />
            <Route path="payment-gateways" element={<PaymentGateway />} />
            <Route path="batch-create" element={<AddBatch />} />
            <Route path="batch-details/:id" element={<BatchDetails />} />

            {/* Testimonials Routes */}
            <Route path="testimonials" element={<Testimonials />} />

            {/* Categories route */}
            <Route path="categories" element={<Categories />} />

            {/* Ebook Routes */}
            <Route path="ebooks" element={<EbookList />} />

            {/* Promotional Items  */}
            <Route path="promotional-items" element={<PromotionalItems />} />

            {/* Discussions */}
            <Route path="discussions" element={<DiscussionList />} />
            <Route path="discussions/:id" element={<DiscussionDetails />} />

            {/* Attendance  */}
            <Route path="attendance" element={<Attendance />} />
            <Route path="payment-collection" element={<PaymentCollection />} />
            {/* Components pages */}

            <Route path="*" element={<Navigate to="/404" />} />


            {/* Super Admin Routes  */}
            <Route path="organizations" element={<Suspense fallback={<Loading />}> <Organizations /> </Suspense>} />

            <Route path="organization-dashboard/:id" element={<Dashboard />} />
            <Route
              path="organization-details/:id"
              element={
                <Suspense fallback={<Loading />}>
                  <OrganizationDetails />
                </Suspense>
              }
            />

            <Route path="pricing-plan" element={<PricingPlan />} />
            <Route path="pricing-plan-create" element={<PricingPlanCreate />} />
            <Route path="pricing-plan-details/:id" element={<PricingPlanDetails />} />

          </Route>
        </Route>


        <Route
          path="/sample-page"
          element={
            <Suspense fallback={<Loading />}>
              <SamplePage />
            </Suspense>
          }
        />

        <Route
          path="/404"
          element={
            <Suspense fallback={<Loading />}>
              <Error />
            </Suspense>
          }
        />
      </Routes>
    </main>
  );
}

export default App;
