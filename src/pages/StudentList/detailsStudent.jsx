import React from "react";
import { useNavigate, useParams } from "react-router-dom";
import moment from "moment";
import Icon from "@/components/ui/Icon";
import { useDispatch } from "react-redux";
import { setEditShowModal, setEditData } from "@/features/commonSlice";
import { useGetApiQuery } from "@/store/api/master/commonSlice";
import avatar from "@/assets/images/avatar/av-1.svg";
import EditStudent from "./editStudent";
import { Mail, Phone, Award, MapPin, BookOpen, User, Calendar, Heart, Flag } from "lucide-react";

// Modern detail item with icon
const DetailItem = ({ label, value, icon }) => (
  <div className="flex items-start space-x-3 py-2">
    {icon && (
      <div className="flex-shrink-0 mt-1">
        {icon}
      </div>
    )}
    <div className="flex-1">
      <p className="text-sm text-gray-500 font-medium">{label}</p>
      <p className="text-base text-gray-800">{value || "---"}</p>
    </div>
  </div>
);

// Section card with title
const SectionCard = ({ title, icon, children }) => (
  <div className="bg-white rounded-lg shadow-sm border border-gray-100 overflow-hidden">
    <div className="border-b border-gray-100 px-5 py-3 flex items-center">
      {icon && <span className="text-primary-500 mr-2">{icon}</span>}
      <h3 className="text-base font-medium text-gray-800">{title}</h3>
    </div>
    <div className="p-5">{children}</div>
  </div>
);

const detailsStudent = () => {
  const dispatch = useDispatch();
  const navigate = useNavigate();
  const { id } = useParams();

  const {
    data: student,
    isLoading,
  } = useGetApiQuery(`admin/student-details-for-admin?id=${id}`);

  const handleEditStudentClick = () => {
    dispatch(setEditData(student));
    dispatch(setEditShowModal(true));
  };

  if (isLoading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-primary-500"></div>
      </div>
    );
  }

  if (!student) {
    return (
      <div className="flex flex-col items-center justify-center h-64">
        <Icon icon="heroicons-outline:exclamation-circle" className="w-12 h-12 text-gray-400 mb-2" />
        <h2 className="text-xl font-medium text-gray-600">No student details available</h2>
        <button
          onClick={() => navigate("/student-list")}
          className="mt-4 px-4 py-2 text-sm font-medium text-primary-600 bg-primary-50 rounded-md hover:bg-primary-100"
        >
          Back to Student List
        </button>
      </div>
    );
  }

  return (
    <>
      {/* Header with action buttons */}
      <div className="flex justify-between items-center mb-6">
        <h1 className="text-2xl font-bold text-gray-800">Student Profile</h1>
        <div className="flex space-x-3">
          <button
            onClick={() => navigate("/student-list")}
            className="flex items-center px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50"
          >
            <Icon
              icon="material-symbols:arrow-back"
              className="w-4 h-4 mr-2"
            />
            Back to List
          </button>
          <button
            onClick={handleEditStudentClick}
            className="flex items-center px-4 py-2 text-sm font-medium text-white bg-primary-500 rounded-md hover:bg-primary-600"
          >
            <Icon
              icon="akar-icons:edit"
              className="w-4 h-4 mr-2"
            />
            Edit Profile
          </button>
        </div>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* Left column - Profile card */}
        <div className="lg:col-span-1 space-y-6">
          {/* Profile Card */}
          <div className="bg-white rounded-lg shadow-sm border border-gray-100 overflow-hidden">
            <div className="p-6 flex flex-col items-center text-center">
              <div className="relative mb-4 group">
                <div className="h-32 w-32 rounded-full overflow-hidden ring-4 ring-white shadow-md">
                  <img
                    src={
                      student.image
                        ? import.meta.env.VITE_ASSET_HOST_URL + student.image
                        : avatar
                    }
                    alt={student.name || "Student"}
                    className="w-full h-full object-cover"
                  />
                </div>
              </div>

              <h2 className="text-xl font-bold text-gray-800 mb-1">
                {student.name}
              </h2>

              {student.student_code && (
                <p className="text-sm text-gray-500 mb-3">
                  ID: {student.student_code}
                </p>
              )}

              <div className="flex flex-wrap items-center justify-center gap-2 mb-4">
                <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
                  student.is_active ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'
                }`}>
                  {student.is_active ? 'Active' : 'Inactive'}
                </span>
              </div>

              <div className="w-full space-y-2 border-t border-gray-100 pt-4">
                {student.email && (
                  <div className="flex items-center text-sm">
                    <Mail size={16} className="text-gray-400 mr-2" />
                    <span className="text-gray-600">{student.email}</span>
                  </div>
                )}

                {student.contact_no && (
                  <div className="flex items-center text-sm">
                    <Phone size={16} className="text-gray-400 mr-2" />
                    <span className="text-gray-600">{student.contact_no}</span>
                  </div>
                )}

                {student.alternative_contact_no && (
                  <div className="flex items-center text-sm">
                    <Phone size={16} className="text-gray-400 mr-2" />
                    <span className="text-gray-600">{student.alternative_contact_no} (Alt)</span>
                  </div>
                )}
              </div>
            </div>
          </div>

          {/* Status Card */}
          <div className="bg-white rounded-lg shadow-sm border border-gray-100 overflow-hidden">
            <div className="border-b border-gray-100 px-5 py-3">
              <h3 className="text-base font-medium text-gray-800">Status Information</h3>
            </div>
            <div className="p-5 space-y-3">
              <div className="flex justify-between items-center">
                <span className="text-sm text-gray-600">Active Status</span>
                <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
                  student.is_active ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'
                }`}>
                  {student.is_active ? 'Active' : 'Inactive'}
                </span>
              </div>

              {student.created_at && (
                <div className="flex justify-between items-center">
                  <span className="text-sm text-gray-600">Registration Date</span>
                  <span className="text-sm text-gray-800">
                    {moment(student.created_at).format("MMM D, YYYY")}
                  </span>
                </div>
              )}
            </div>
          </div>
        </div>

        {/* Right column - Details sections */}
        <div className="lg:col-span-2 space-y-6">
          {/* Education section */}
          <SectionCard title="Education Details" icon={<BookOpen size={18} />}>
            <div className="grid md:grid-cols-2 gap-6">
              <DetailItem
                icon={<Award size={18} className="text-gray-400" />}
                label="Education"
                value={student.education}
              />
              <DetailItem
                icon={<MapPin size={18} className="text-gray-400" />}
                label="Institute"
                value={student.institute}
              />
            </div>
          </SectionCard>

          {/* Address section */}
          {(student.current_address || student.permanent_address) && (
            <SectionCard title="Address Information" icon={<MapPin size={18} />}>
              <div className="grid md:grid-cols-2 gap-6">
                {student.current_address && (
                  <DetailItem
                    icon={<MapPin size={18} className="text-gray-400" />}
                    label="Current Address"
                    value={student.current_address}
                  />
                )}
                {student.permanent_address && (
                  <DetailItem
                    icon={<MapPin size={18} className="text-gray-400" />}
                    label="Permanent Address"
                    value={student.permanent_address}
                  />
                )}
              </div>
            </SectionCard>
          )}

          {/* Personal details section */}
          <SectionCard title="Personal Information" icon={<User size={18} />}>
            <div className="grid md:grid-cols-2 gap-6">
              {student.date_of_birth && (
                <DetailItem
                  icon={<Calendar size={18} className="text-gray-400" />}
                  label="Date of Birth"
                  value={moment(student.date_of_birth).format("MMMM D, YYYY")}
                />
              )}
              {student.gender && (
                <DetailItem
                  icon={<User size={18} className="text-gray-400" />}
                  label="Gender"
                  value={student.gender}
                />
              )}
              {student.blood_group && (
                <DetailItem
                  icon={<Heart size={18} className="text-gray-400" />}
                  label="Blood Group"
                  value={student.blood_group}
                />
              )}
              {student.religion && (
                <DetailItem
                  icon={<Flag size={18} className="text-gray-400" />}
                  label="Religion"
                  value={student.religion}
                />
              )}
              {student.marital_status && (
                <DetailItem
                  icon={<User size={18} className="text-gray-400" />}
                  label="Marital Status"
                  value={student.marital_status}
                />
              )}
              {student.father_name && (
                <DetailItem
                  icon={<User size={18} className="text-gray-400" />}
                  label="Father's Name"
                  value={student.father_name}
                />
              )}
              {student.mother_name && (
                <DetailItem
                  icon={<User size={18} className="text-gray-400" />}
                  label="Mother's Name"
                  value={student.mother_name}
                />
              )}
            </div>
          </SectionCard>

          {/* ID Documents section */}
          {(student.nid_no || student.birth_certificate_no || student.passport_no) && (
            <SectionCard title="ID Documents" icon={<Award size={18} />}>
              <div className="grid md:grid-cols-2 gap-6">
                {student.nid_no && (
                  <DetailItem
                    icon={<Award size={18} className="text-gray-400" />}
                    label="NID / Social Security Number"
                    value={student.nid_no}
                  />
                )}
                {student.birth_certificate_no && (
                  <DetailItem
                    icon={<Award size={18} className="text-gray-400" />}
                    label="Birth Certificate No."
                    value={student.birth_certificate_no}
                  />
                )}
                {student.passport_no && (
                  <DetailItem
                    icon={<Award size={18} className="text-gray-400" />}
                    label="Passport Number"
                    value={student.passport_no}
                  />
                )}
              </div>
            </SectionCard>
          )}

          {/* System Information */}
          <SectionCard title="System Information" icon={<Icon icon="heroicons-outline:information-circle" className="w-5 h-5" />}>
            <div className="grid md:grid-cols-2 gap-6">
              {student.created_at && (
                <DetailItem
                  icon={<Calendar size={18} className="text-gray-400" />}
                  label="Created At"
                  value={moment(student.created_at).format("MMMM D, YYYY, h:mm A")}
                />
              )}
              {student.updated_at && (
                <DetailItem
                  icon={<Calendar size={18} className="text-gray-400" />}
                  label="Last Updated"
                  value={moment(student.updated_at).format("MMMM D, YYYY, h:mm A")}
                />
              )}
            </div>
          </SectionCard>
        </div>
      </div>

      {/* Edit Student Modal */}
      <EditStudent student={student} onClose={() => dispatch(setEditShowModal(false))} />
    </>
  );
};

export default detailsStudent;
