import React, { useState, useEffect } from "react";
import Modal from "@/components/ui/Modal";
import InputField from "@/components/ui/InputField";
import Switch from "@/components/ui/Switch";
import Button from "@/components/ui/Button";
import Textarea from "@/components/ui/Textarea";
import Select from "@/components/ui/Select";
import DatePicker from "@/components/partials/common-dateTimePicker/Date";
import NumberInput from "@/components/partials/common-numberInput/NumberInput";
import { Formik, Form, useField } from "formik";
import { editValidationSchema } from "./formSettings";
import { useDispatch, useSelector } from "react-redux";
import { setEditShowModal } from "@/features/commonSlice";
import { useUpdateApiMutation } from "@/store/api/master/commonSlice";
import { useDropzone } from "react-dropzone";
import { X, Camera, Edit2 } from "lucide-react";

// Modern Image Uploader Component
const ImageUploader = ({ name, label, initialImage }) => {
  const [field, meta, helpers] = useField(name);
  const [preview, setPreview] = useState(null);
  const assetBaseURL = import.meta.env.VITE_ASSET_HOST_URL || '';

  const { getRootProps, getInputProps, isDragActive } = useDropzone({
    accept: {
      'image/*': ['.jpeg', '.jpg', '.png', '.gif']
    },
    maxFiles: 1,
    onDrop: (acceptedFiles) => {
      if (acceptedFiles.length > 0) {
        const file = acceptedFiles[0];
        helpers.setValue(file);
        const objectUrl = URL.createObjectURL(file);
        setPreview(objectUrl);
      }
    }
  });

  useEffect(() => {
    // Handle initial image from server
    if (initialImage && typeof initialImage === 'string') {
      setPreview(`${assetBaseURL}${initialImage}`);
    }

    // Cleanup function
    return () => {
      if (preview && preview.startsWith('blob:')) {
        URL.revokeObjectURL(preview);
      }
    };
  }, [initialImage, assetBaseURL]);

  const handleRemove = (e) => {
    e.stopPropagation();
    helpers.setValue(null);
    setPreview(null);
  };

  return (
    <div className="mb-2">
      {label && (
        <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
          {label}
        </label>
      )}

      <div
        {...getRootProps()}
        className={`relative cursor-pointer ${preview ? 'h-40 w-full' : 'h-40 border-2 border-dashed rounded-lg p-2 flex flex-col items-center justify-center'}
        ${isDragActive ? 'border-primary-500 bg-primary-50' : 'border-gray-300 hover:border-primary-500'}`}
      >
        <input {...getInputProps()} />

        {preview ? (
          <>
            <img
              src={preview}
              alt="Preview"
              className="h-full w-full object-cover rounded-lg"
            />
            <div className="absolute inset-0 bg-black bg-opacity-40 opacity-0 hover:opacity-100 transition-opacity duration-200 rounded-lg flex items-center justify-center">
              <div className="flex gap-2">
                <button
                  type="button"
                  onClick={(e) => {
                    e.stopPropagation();
                    getRootProps().onClick(e);
                  }}
                  className="p-1 bg-white rounded-full text-gray-700 hover:text-primary-500"
                >
                  <Edit2 size={16} />
                </button>
                <button
                  type="button"
                  onClick={handleRemove}
                  className="p-1 bg-white rounded-full text-gray-700 hover:text-red-500"
                >
                  <X size={16} />
                </button>
              </div>
            </div>
          </>
        ) : (
          <div className="text-center">
            <Camera className="mx-auto h-8 w-8 text-gray-400" />
            <div className="mt-1">
              <p className="text-xs text-gray-600 dark:text-gray-400">
                {isDragActive ? 'Drop image here' : 'Drag & drop or click'}
              </p>
              <p className="text-xs text-gray-500">
                PNG, JPG, GIF
              </p>
            </div>
          </div>
        )}
      </div>

      {meta.touched && meta.error && (
        <div className="text-red-500 text-xs mt-1">{meta.error}</div>
      )}
    </div>
  );
};

const editStudent = () => {
  const { editData } = useSelector((state) => state.commonReducer);
  const dispatch = useDispatch();
  const { showEditModal } = useSelector((state) => state.commonReducer);
  const [updateApi, { isLoading }] = useUpdateApiMutation();

  // Switch States
  const [isActive, setIsActive] = useState(editData?.is_active || false);

  // Select options
  const genderOptions = [
    { label: "Male", value: "Male" },
    { label: "Female", value: "Female" },
    { label: "Others", value: "Others" },
  ];

  const bloodGroupOptions = [
    { label: "A+", value: "A+" },
    { label: "A-", value: "A-" },
    { label: "B+", value: "B+" },
    { label: "B-", value: "B-" },
    { label: "O+", value: "O+" },
    { label: "O-", value: "O-" },
    { label: "AB+", value: "AB+" },
    { label: "AB-", value: "AB-" }
  ];

  const religionOptions = [
    { label: "Islam", value: "Islam" },
    { label: "Hinduism", value: "Hinduism" },
    { label: "Christianity", value: "Christianity" },
    { label: "Buddhism", value: "Buddhism" },
    { label: "Others", value: "Others" }
  ];

  const maritalStatusOptions = [
    { label: "Single", value: "Single" },
    { label: "Married", value: "Married" },
    { label: "Divorced", value: "Divorced" },
    { label: "Widowed", value: "Widowed" }
  ];

  const onSubmit = async (values, { resetForm }) => {
    try {
      const formData = new FormData();

      // Basic information
      formData.append("name", values.name || "");

      // Only send email if it has been changed
      if (values.email !== editData.email) {
        formData.append("email", values.email || "");
      }

      formData.append("contact_no", values.contact_no || "");
      formData.append("alternative_contact_no", values.alternative_contact_no || "");
   

      // Handle image upload
      if (values.image && values.image instanceof File) {
        formData.append("image", values.image);
      }

      // Education & Professional details
      formData.append("education", values.education || "");
      formData.append("institute", values.institute || "");
      formData.append("bio", values.bio || "");

      // Personal details
      formData.append("gender", values.gender || "");
      formData.append("blood_group", values.blood_group || "");
      formData.append("father_name", values.father_name || "");
      formData.append("mother_name", values.mother_name || "");
      formData.append("religion", values.religion || "");
      formData.append("marital_status", values.marital_status || "");

      if (values.date_of_birth) {
        const formattedDate = typeof values.date_of_birth === 'string'
          ? values.date_of_birth
          : values.date_of_birth.format('YYYY-MM-DD');
        formData.append("date_of_birth", formattedDate);
      }

      // Address details
      formData.append("current_address", values.current_address || "");
      formData.append("permanent_address", values.permanent_address || "");

      // ID documents
      formData.append("nid_no", values.nid_no || "");
      formData.append("birth_certificate_no", values.birth_certificate_no || "");
      formData.append("passport_no", values.passport_no || "");

      // Status flags
      formData.append("is_active", isActive ? 1 : 0);
      formData.append("_method", "PUT");

      const response = await updateApi({
        end_point: "admin/student-update/" + editData.id,
        body: formData,
      });

      if (response.error) {
        console.error("Error updating student:", response.error);
      } else {
        dispatch(setEditShowModal(false));
      }
    } catch (error) {
      console.error("Error in form submission:", error);
    }
  };

  return (
    <Modal
      activeModal={showEditModal}
      onClose={() => dispatch(setEditShowModal(false))}
      title="Update Student Profile"
      className="max-w-6xl"
      centered
    >
      <Formik
        validationSchema={editValidationSchema}
        initialValues={editData}
        onSubmit={onSubmit}
      >
        {({ setFieldValue, values, isSubmitting }) => (
          <Form className="space-y-6">
            <div className="grid md:grid-cols-3 gap-6">
              {/* Left column - Image uploader and status toggles */}
              <div className="md:col-span-1">
                <ImageUploader
                  name="image"
                  label="Profile Photo"
                  initialImage={editData.image}
                />

                <div className="bg-gray-50 p-4 rounded-lg border border-gray-200">
                  <h3 className="text-sm font-medium text-gray-700 mb-3">Basic Information</h3>
                  <div className="space-y-4">
                    <InputField
                      label="Full Name"
                      name="name"
                      type="text"
                      placeholder="Enter student's full name"
                      required
                    />

                    <div className="grid grid-cols-1 gap-4">
                      <InputField
                        label="Email Address"
                        name="email"
                        type="email"
                        placeholder="Enter email address"
                      />
                    </div>

                    <div className="grid grid-cols-1 gap-4">
                      <NumberInput
                        label="Contact Number"
                        name="contact_no"
                        type="text"
                        placeholder="Enter contact number"
                      />

                      <NumberInput
                        label="Alternative Contact"
                        name="alternative_contact_no"
                        type="text"
                        placeholder="Enter alternative contact"
                      />
                    </div>
                  </div>
                </div>

                <div className="mt-6 bg-gray-50 p-4 rounded-lg border border-gray-200">
                  <h3 className="text-sm font-medium text-gray-700 mb-3">Status Settings</h3>

                  <div className="space-y-3">
                    <div className="flex items-center justify-between">
                      <label className="block text-sm text-gray-700">Active Status</label>
                      <Switch
                        activeClass="bg-success-500"
                        value={isActive}
                        name="is_active"
                        onChange={() => setIsActive(!isActive)}
                      />
                    </div>
                  </div>
                </div>
              </div>

              {/* Right column - Form fields */}
              <div className="md:col-span-2 space-y-5">
                {/* Bio section */}
                <div className="bg-gray-50 p-4 rounded-lg border border-gray-200">
                  <div className="space-y-4">
                    <Textarea
                      label="Bio"
                      name="bio"
                      placeholder="Enter a short bio"
                      row={3}
                      value={values.bio || ""}
                      onChange={(e) => setFieldValue("bio", e.target.value)}
                    />
                  </div>
                </div>

                {/* Education & Professional Details */}
                <div className="bg-gray-50 p-4 rounded-lg border border-gray-200">
                  <h3 className="text-sm font-medium text-gray-700 mb-3">Education & Professional Details</h3>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <InputField
                      label="Education"
                      name="education"
                      type="text"
                      placeholder="Highest education level"
                    />

                    <InputField
                      label="Institute"
                      name="institute"
                      type="text"
                      placeholder="Educational institute"
                    />

                  </div>
                </div>

                {/* Personal Details */}
                <div className="bg-gray-50 p-4 rounded-lg border border-gray-200">
                  <h3 className="text-sm font-medium text-gray-700 mb-3">Personal Details</h3>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <Select
                      label="Gender"
                      name="gender"
                      placeholder="Select gender"
                      options={genderOptions}
                      onChange={(e) => setFieldValue("gender", e.target.value)}
                      defaultValue={values.gender}
                    />

                    <Select
                      label="Blood Group"
                      name="blood_group"
                      placeholder="Select blood group"
                      options={bloodGroupOptions}
                      onChange={(e) => setFieldValue("blood_group", e.target.value)}
                      defaultValue={values.blood_group}
                    />

                    <InputField
                      label="Father's Name"
                      name="father_name"
                      type="text"
                      placeholder="Enter father's name"
                    />

                    <InputField
                      label="Mother's Name"
                      name="mother_name"
                      type="text"
                      placeholder="Enter mother's name"
                    />

                    <Select
                      label="Religion"
                      name="religion"
                      placeholder="Select religion"
                      options={religionOptions}
                      onChange={(e) => setFieldValue("religion", e.target.value)}
                      defaultValue={values.religion}
                    />

                    <Select
                      label="Marital Status"
                      name="marital_status"
                      placeholder="Select marital status"
                      options={maritalStatusOptions}
                      onChange={(e) => setFieldValue("marital_status", e.target.value)}
                      defaultValue={values.marital_status}
                    />

                    <DatePicker
                      label="Date of Birth"
                      name="date_of_birth"
                      placeholder="YYYY-MM-DD"
                      format="YYYY-MM-DD"
                      defaultValue={values.date_of_birth}
                      onChange={(date) => setFieldValue("date_of_birth", date)}
                    />
                  </div>
                </div>

                {/* Address Information */}
                <div className="bg-gray-50 p-4 rounded-lg border border-gray-200">
                  <h3 className="text-sm font-medium text-gray-700 mb-3">Address Information</h3>
                  <div className="space-y-4">
                    <Textarea
                      label="Current Address"
                      name="current_address"
                      placeholder="Enter current address"
                      row={2}
                      value={values.current_address || ""}
                      onChange={(e) => setFieldValue("current_address", e.target.value)}
                    />

                    <Textarea
                      label="Permanent Address"
                      name="permanent_address"
                      placeholder="Enter permanent address"
                      row={2}
                      value={values.permanent_address || ""}
                      onChange={(e) => setFieldValue("permanent_address", e.target.value)}
                    />
                  </div>
                </div>

                {/* ID Documents */}
                <div className="bg-gray-50 p-4 rounded-lg border border-gray-200">
                  <h3 className="text-sm font-medium text-gray-700 mb-3">ID Documents</h3>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <InputField
                      label="NID / Social Security Number"
                      name="nid_no"
                      type="text"
                      placeholder="Enter NID or Social Security number"
                    />

                    <InputField
                      label="Birth Certificate No."
                      name="birth_certificate_no"
                      type="text"
                      placeholder="Enter birth certificate number"
                    />

                    <InputField
                      label="Passport Number"
                      name="passport_no"
                      type="text"
                      placeholder="Enter passport number"
                    />
                  </div>
                </div>
              </div>
            </div>

            <div className="flex justify-end space-x-3 pt-4 border-t border-gray-200">
              <Button
                text="Cancel"
                className="btn-outline-dark"
                onClick={() => dispatch(setEditShowModal(false))}
                type="button"
              />
              <Button
                text="Save Changes"
                className="btn-primary"
                type="submit"
                isLoading={isSubmitting || isLoading}
                disabled={isSubmitting || isLoading}
              />
            </div>
          </Form>
        )}
      </Formik>
    </Modal>
  );
};

export default editStudent;

