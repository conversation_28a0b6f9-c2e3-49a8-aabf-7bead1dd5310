import React, {useState} from "react";

import Card from "@/components/ui/Card";
import { useGetSubjectListQuery } from "@/store/api/master/rowContentSubjectListSlice";
import Badge from "@/components/ui/Badge";
import Button from "@/components/ui/Button";
import { useDispatch, useSelector } from "react-redux";
import { setEditData } from "@/features/commonSlice";
import { useGetApiQuery } from "@/store/api/master/commonSlice";
import { useParams } from "react-router-dom";
import CreateQuestion from "./createQuestion";
import CreateWritten from "./createWritten";
import EditWritten from "./editWritten";
import WrittenQuestion from "./writtenQuestion";
import BulkUpload from "./bulkUpload";
import Delete from "./Delete";
import QuestionItems from "./QuestionItems";
import CreateTrueFalse from "./createTrueFalse";
import CreateMatching from "./createMatching";
import CreateFillBlanks from "./createFillBlanks";
import { Icon } from "@iconify/react";
import Dropdown from "@/components/ui/Dropdown";

const index = () => {
  const [showModal, setShowModal] = useState(false);
  const [showEditWrittenModal, setEditShowWrittenModal] = useState(false);
  const [showWrittenModal, setShowWrittenModal] = useState(false);
  const [showBulkModal, setShowBulkModal] = useState(false);
  const [showTrueFalseModal, setShowTrueFalseModal] = useState(false);
  const [showMatchingModal, setShowMatchingModal] = useState(false);
  const [showFillBlanksModal, setShowFillBlanksModal] = useState(false);
  const [editQuestionData, setEditQuestionData] = useState(null);
  const [editTrueFalseData, setEditTrueFalseData] = useState(null);
  const [editFillBlanksData, setEditFillBlanksData] = useState(null);
  const [editMatchingData, setEditMatchingData] = useState(null);
  const dispatch = useDispatch();
  const { id } = useParams();
  // const questionList = useGetQuestionListQuery(id)?.data;
  const questionList = useGetApiQuery('admin/question-list-by-quiz/' + id)?.data;
  const quiz = useGetApiQuery('admin/quiz-details-by-id/' + id)?.data;
  // const quiz = useGetQuizDetailsQuery(id)?.data;
  const [deleteData, setDeleteData] = useState(null);
  const [showDeleteModal, setShowDeleteModal] = useState(false);


  return (
    <>
      <Card>
        <div className="flex bg-white p-4 rounded-lg shadow border dark:border-gray-700">
          <div className="w-1/3 px-4">
            <div className="flex gap-4">
              <div className=""> <b>Quiz Title :</b> {quiz?.title}</div>
              {/* <div className="col-span-2"></div> */}
            </div>
            <div className="flex gap-4">
              <div className="col-span-1 font-bold">Quiz Code : </div>
              <div className="col-span-2">{quiz?.quiz_code}</div>
            </div>

            <div className="flex gap-4">
              <div className="col-span-1 font-bold">Total Question : </div>
              <div className="col-span-2">{quiz?.number_of_question}</div>
            </div>
          </div>
          <div className="w-1/3 px-4">
            <div className="flex gap-4">
              <div className="col-span-1 font-bold">Total Mark : </div>
              <div className="col-span-2">{quiz?.total_mark}</div>
            </div>
            <div className="flex gap-4">
              <div className="col-span-1 font-bold">Positive Mark : </div>
              <div className="col-span-2">{quiz?.positive_mark}</div>
            </div>
            <div className="flex gap-4">
              <div className="col-span-1 font-bold">Negative Mark : </div>
              <div className="col-span-demoQuestionFile2">{quiz?.negative_mark}</div>
            </div>
          </div>
          <div className="w-1/3 px-4">

          <div className="flex gap-4">
              <div className="col-span-1 font-bold">Duration : </div>
              <div className="col-span-2">{quiz?.duration} Minutes  </div>
            </div>
          </div>
        </div>
        <div className="flex mt-2 pb-2 bg-white p-4 rounded-lg shadow border dark:border-gray-700">
          <p><b>Description: </b> {quiz?.description} </p>
        </div>

<div className="flex gap-4 items-center mt-4">
  <Dropdown
    label={
      <button
        className="px-4 py-2 text-white bg-blue-600 hover:bg-blue-700 rounded-md text-sm flex items-center gap-2 transition-all duration-200 ease-in-out shadow-md"
      >
        <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
          <path d="M10 5a1 1 0 011 1v3h3a1 1 0 110 2h-3v3a1 1 0 11-2 0v-3H6a1 1 0 110-2h3V6a1 1 0 011-1z" />
        </svg>
        Add New Question
      </button>
    }
    classMenuItems="left-0 w-[220px] top-[110%]"
  >
    <div>
      <div
        onClick={() => setShowModal(true)}
        className="flex items-center px-4 py-2 hover:bg-slate-100 cursor-pointer text-slate-600 dark:text-slate-300 dark:hover:bg-slate-600 dark:hover:bg-opacity-50"
      >
        <span className="block text-xl ltr:mr-3 rtl:ml-3">
          <Icon icon="heroicons-outline:check-circle" />
        </span>
        <span className="block text-sm">MCQ</span>
      </div>
      <div
        onClick={() => setShowTrueFalseModal(true)}
        className="flex items-center px-4 py-2 hover:bg-slate-100 cursor-pointer text-slate-600 dark:text-slate-300 dark:hover:bg-slate-600 dark:hover:bg-opacity-50"
      >
        <span className="block text-xl ltr:mr-3 rtl:ml-3">
          <Icon icon="heroicons-outline:switch-horizontal" />
        </span>
        <span className="block text-sm">True/False</span>
      </div>
      <div
        onClick={() => setShowMatchingModal(true)}
        className="flex items-center px-4 py-2 hover:bg-slate-100 cursor-pointer text-slate-600 dark:text-slate-300 dark:hover:bg-slate-600 dark:hover:bg-opacity-50"
      >
        <span className="block text-xl ltr:mr-3 rtl:ml-3">
          <Icon icon="heroicons-outline:link" />
        </span>
        <span className="block text-sm">Matching</span>
      </div>
      <div
        onClick={() => setShowFillBlanksModal(true)}
        className="flex items-center px-4 py-2 hover:bg-slate-100 cursor-pointer text-slate-600 dark:text-slate-300 dark:hover:bg-slate-600 dark:hover:bg-opacity-50"
      >
        <span className="block text-xl ltr:mr-3 rtl:ml-3">
          <Icon icon="heroicons-outline:pencil-alt" />
        </span>
        <span className="block text-sm">Fill in the Blank</span>
      </div>
    </div>
  </Dropdown>

  <button
    className="px-4 py-2 text-white bg-blue-600 hover:bg-blue-700 rounded-md text-sm flex items-center gap-2 transition-all duration-200 ease-in-out shadow-md"
    onClick={() => setShowBulkModal(true)}
  >
    <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
      <path d="M3 3a1 1 0 011-1h12a1 1 0 011 1v1h-2V4H5v1H3V3zm2 5a1 1 0 000 2h1v1H5a1 1 0 100 2h1v1H5a1 1 0 000 2h10a1 1 0 100-2h-1v-1h1a1 1 0 100-2h-1v-1h1a1 1 0 100-2H5z" />
    </svg>
    Bulk Upload
  </button>

  <a
    href={`${import.meta.env.VITE_ASSET_HOST_URL}dummy_questions.csv`}
    download
    className="px-4 py-2 text-white bg-blue-600 hover:bg-blue-700 rounded-md text-sm flex items-center gap-2 transition-all duration-200 ease-in-out shadow-md"
  >
    <svg
      xmlns="http://www.w3.org/2000/svg"
      className="h-5 w-5"
      viewBox="0 0 20 20"
      fill="currentColor"
    >
      <path
        fillRule="evenodd"
        d="M3 17a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm3.293-7.707a1 1 0 011.414 0L9 10.586V3a1 1 0 112 0v7.586l1.293-1.293a1 1 0 111.414 1.414l-3 3a1 1 0 11-1.414 0l-3-3a1 1 0 010-1.414z"
        clipRule="evenodd"
      />
    </svg>
    Download Sample CSV
  </a>
</div>


         {quiz?.written_questions ? (

        <div className="relative flex gap-4 mt-4 rounded-lg shadow border dark:border-gray-700">

          <div className="absolute top-0 right-0">
            <Button
              className="btn btn-sm"
              variant="outline"
              onClick={() => setEditShowWrittenModal(true)}
            >
              <Icon icon="material-symbols:edit-outline" className="text-lg" />

            </Button>
          </div>

        {/* {quiz?.written_questions.map((question, index) => ( */}
          <div className="">
            <WrittenQuestion
              chapter_quiz_id={quiz?.written_questions.chapter_quiz_id}
              created_at={quiz?.written_questions.created_at}
              created_by={quiz?.written_questions.created_by}
              deleted_at={quiz?.written_questions.deleted_at}
              description={quiz?.written_questions.description}
              id={quiz?.written_questions.id}
              is_active={quiz?.written_questions.is_active}
              marks={quiz?.written_questions.marks}
              no_of_question={quiz?.written_questions.no_of_question}
              organization_id={quiz?.written_questions.organization_id}
              question_attachment={quiz?.written_questions.question_attachment}
              updated_at={quiz?.written_questions.updated_at}
              instruction={quiz?.written_questions.instruction}
              duration={quiz?.written_questions.duration}
            />
          </div>
        </div>
         ):

      <div className="relative flex gap-4 mt-4">
<button
  className="w-56 h-32 p-6 bg-white text-black rounded-lg shadow-lg hover:bg-gray-100 hover:shadow-lg transition-all duration-200 ease-in-out flex flex-col items-center justify-center gap-2"
  onClick={() => setShowWrittenModal(true)}
>
  <div className="relative">
    {/* Document Icon */}
    <svg
      xmlns="http://www.w3.org/2000/svg"
      className="h-10 w-10"
      viewBox="0 0 20 20"
      fill="currentColor"
    >
      <path d="M4 3a2 2 0 012-2h8a2 2 0 012 2v14a2 2 0 01-2 2H6a2 2 0 01-2-2V3zm2 3h8a1 1 0 100-2H6a1 1 0 100 2zm0 4h5a1 1 0 100-2H6a1 1 0 100 2z" />
    </svg>

  </div>
  <span className="text-base font-semibold">Add Written Question</span>
</button>


      </div>
         }

         <QuestionItems
            quiz_items={quiz?.quiz_items}
            onDelete={(item) => {
              setDeleteData(item);
              setShowDeleteModal(true);
            }}
            onEdit={(item) => {
              console.log("Edit item:", item);
              if (item.type === 'mcq') {
                console.log("Setting MCQ data for edit:", item);
                setEditQuestionData(item);
                setShowModal(true);
              } else if (item.type === 'true_false') {
                console.log("Setting true/false data for edit:", item);
                setEditTrueFalseData(item);
                setShowTrueFalseModal(true);
              } else if (item.type === 'fill_in_blank' || item.type === 'fill_blanks') {
                console.log("Setting fill in blanks data for edit:", item);
                setEditFillBlanksData(item);
                setShowFillBlanksModal(true);
              } else if (item.type === 'matching') {
                console.log("Setting matching data for edit:", item);
                setEditMatchingData(item);
                setShowMatchingModal(true);
              } else {
                alert(`Edit functionality for ${item.type} question will be implemented soon.`);
              }
            }}
          />
        <div className="grid grid-cols-2 gap-4 mt-4">
          {questionList?.map((question, index) => (
            <div key={index} className="bg-white p-4 rounded-lg shadow border dark:border-gray-700">
              <div className="grid grid-cols-9">
                <div className="col-span-8">
                  <p className="mb-2"><b>{index + 1}.</b>  {question?.question_text} </p>
                </div>
                <div className="col-span-1 text-right">
                  <button onClick={() => {
                    setDeleteData(question);
                    setShowDeleteModal(true);
                  }} className="btn btn-sm text-2xl">

                   <Icon icon="material-symbols:delete-outline-rounded" className="text-lg text-red-500"/>
                  </button>

                  {/* <Badge className="bg-success-500 text-white"> {question?.question_set_name} </Badge> */}
                </div>
              </div>
              {question?.question_image && (
                <img className="w-auto h-[70px] mt-2" src={`${import.meta.env.VITE_ASSET_HOST_URL ?? ''}${question?.question_image}`} alt={question?.question_image}/>
              )}
              <div className="grid grid-cols-2 gap-2">
                <div className="flex items-center gap-2 p-2 border rounded-lg">
                <div className={`bg-${question?.answer1 ? 'green-500' : 'white'} text-${question?.answer1 ? 'white' : 'gray-500'} rounded-full w-6 h-6 flex items-center justify-center`}>A</div>
                  <p className="text-sm">{question?.option1}</p>

                  {question?.option1_image && (
                    <img className="w-auto h-[70px] mt-2" src={`${import.meta.env.VITE_ASSET_HOST_URL ?? ''}${question?.option1_image}`} alt={question?.option1}/>
                  )}
                </div>
                <div className="flex items-center gap-2 p-2 border rounded-lg">
                <div className={`bg-${question?.answer2 ? 'green-500' : 'white'} text-${question?.answer2 ? 'white' : 'gray-500'} rounded-full w-6 h-6 flex items-center justify-center`}>B</div>
                  <p className="text-sm">{question?.option2}</p>
                  {question?.option2_image && (
                    <img className="w-auto h-[70px] mt-2" src={`${import.meta.env.VITE_ASSET_HOST_URL ?? ''}${question?.option2_image}`} alt={question?.option2}/>
                  )}
                </div>
              </div>
              <div className="grid grid-cols-2 gap-2 mt-2">
                <div className="flex items-center gap-2 p-2 border rounded-lg">
                <div className={`bg-${question?.answer3 ? 'green-500' : 'white'} text-${question?.answer3 ? 'white' : 'gray-500'} rounded-full w-6 h-6 flex items-center justify-center`}>C</div>
                  <p className="text-sm">{question?.option3}</p>
                  {question?.option3_image && (
                    <img className="w-auto h-[70px] mt-2" src={`${import.meta.env.VITE_ASSET_HOST_URL ?? ''}${question?.option3_image}`} alt={question?.option3}/>
                  )}
                </div>
                <div className="flex items-center gap-2 p-2 border rounded-lg">
                  <div className={`bg-${question?.answer4 ? 'green-500' : 'white'} text-${question?.answer4 ? 'white' : 'gray-500'} rounded-full w-6 h-6 flex items-center justify-center`}>D</div>
                  <p className="text-sm">{question?.option4}</p>
                  {question?.option4_image && (
                    <img className="w-auto h-[70px] mt-2" src={`${import.meta.env.VITE_ASSET_HOST_URL ?? ''}${question?.option4_image}`} alt={question?.option4}/>
                  )}
                </div>

                <div className="grid grid-cols-1 gap-2 mt-2">
                  <p className="text-sm"> <b>Explanation: </b> {question?.explanation_text}</p>

                  {question?.explanation_image && (
                    <img className="w-auto h-[70px] mt-2" src={`${import.meta.env.VITE_ASSET_HOST_URL ?? ''}${question?.explanation_image}`} alt={question?.explanation_text}/>
                  )}
                </div>
              </div>
            </div>
          ))}
        </div>
      </Card>
      <CreateQuestion
        showModal={showModal}
        setShowModal={(value) => {
          setShowModal(value);
          if (!value) setEditQuestionData(null);
        }}
        quiz={quiz}
        editData={editQuestionData}
      />
      <CreateWritten showWrittenModal={showWrittenModal} setShowWrittenModal={setShowWrittenModal} quiz={quiz} />
      <EditWritten showEditWrittenModal={showEditWrittenModal} setEditShowWrittenModal={setEditShowWrittenModal} written_questions={quiz?.written_questions} />
      <BulkUpload showModal={showBulkModal} setShowModal={setShowBulkModal} quiz={quiz} />
      <CreateTrueFalse
        showTrueFalseModal={showTrueFalseModal}
        setShowTrueFalseModal={(value) => {
          setShowTrueFalseModal(value);
          if (!value) setEditTrueFalseData(null);
        }}
        quiz={quiz}
        editData={editTrueFalseData}
      />
      <CreateMatching
        showMatchingModal={showMatchingModal}
        setShowMatchingModal={(value) => {
          setShowMatchingModal(value);
          if (!value) setEditMatchingData(null); // Clear edit data when closing modal
        }}
        quiz={quiz}
        editData={editMatchingData}
      />
      <CreateFillBlanks
        showFillBlanksModal={showFillBlanksModal}
        setShowFillBlanksModal={(value) => {
          setShowFillBlanksModal(value);
          if (!value) setEditFillBlanksData(null); // Clear edit data when closing modal
        }}
        quiz={quiz}
        editData={editFillBlanksData}
      />

      <Delete
        showDeleteModal={showDeleteModal}
        setShowDeleteModal={setShowDeleteModal}
        data={deleteData}
      />
    </>
  );
};

export default index;