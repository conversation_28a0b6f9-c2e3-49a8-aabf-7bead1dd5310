import React from "react";
import Chart from "react-apexcharts";
import { useTranslation } from "react-i18next";

const shapeLine1 = {
  series: [
    {
      data: [800, 600, 1000, 800, 600, 1000, 800, 900],
    },
  ],
  options: {
    chart: {
      toolbar: {
        autoSelected: "pan",
        show: false,
      },
      offsetX: 0,
      offsetY: 0,
      zoom: {
        enabled: false,
      },
      sparkline: {
        enabled: true,
      },
    },
    dataLabels: {
      enabled: false,
    },
    stroke: {
      curve: "smooth",
      width: 2,
    },
    colors: ["#00EBFF"],
    tooltip: {
      theme: "light",
    },
    grid: {
      show: false,
      padding: {
        left: 0,
        right: 0,
      },
    },
    yaxis: {
      show: false,
    },
    fill: {
      type: "solid",
      opacity: [0.1],
    },
    legend: {
      show: false,
    },
    xaxis: {
      low: 0,
      offsetX: 0,
      offsetY: 0,
      show: false,
      labels: {
        low: 0,
        offsetX: 0,
        show: false,
      },
      axisBorder: {
        low: 0,
        offsetX: 0,
        show: false,
      },
    },
  },
};
const shapeLine2 = {
  series: [
    {
      data: [800, 600, 1000, 800, 600, 1000, 800, 900],
    },
  ],
  options: {
    chart: {
      toolbar: {
        autoSelected: "pan",
        show: false,
      },
      offsetX: 0,
      offsetY: 0,
      zoom: {
        enabled: false,
      },
      sparkline: {
        enabled: true,
      },
    },
    dataLabels: {
      enabled: false,
    },
    stroke: {
      curve: "smooth",
      width: 2,
    },
    colors: ["#FB8F65"],
    tooltip: {
      theme: "light",
    },
    grid: {
      show: false,
      padding: {
        left: 0,
        right: 0,
      },
    },
    yaxis: {
      show: false,
    },
    fill: {
      type: "solid",
      opacity: [0.1],
    },
    legend: {
      show: false,
    },
    xaxis: {
      low: 0,
      offsetX: 0,
      offsetY: 0,
      show: false,
      labels: {
        low: 0,
        offsetX: 0,
        show: false,
      },
      axisBorder: {
        low: 0,
        offsetX: 0,
        show: false,
      },
    },
  },
};
const shapeLine3 = {
  series: [
    {
      data: [800, 600, 1000, 800, 600, 1000, 800, 900],
    },
  ],
  options: {
    chart: {
      toolbar: {
        autoSelected: "pan",
        show: false,
      },
      offsetX: 0,
      offsetY: 0,
      zoom: {
        enabled: false,
      },
      sparkline: {
        enabled: true,
      },
    },
    dataLabels: {
      enabled: false,
    },
    stroke: {
      curve: "smooth",
      width: 2,
    },
    colors: ["#5743BE"],
    tooltip: {
      theme: "light",
    },
    grid: {
      show: false,
      padding: {
        left: 0,
        right: 0,
      },
    },
    yaxis: {
      show: false,
    },
    fill: {
      type: "solid",
      opacity: [0.1],
    },
    legend: {
      show: false,
    },
    xaxis: {
      low: 0,
      offsetX: 0,
      offsetY: 0,
      show: false,
      labels: {
        low: 0,
        offsetX: 0,
        show: false,
      },
      axisBorder: {
        low: 0,
        offsetX: 0,
        show: false,
      },
    },
  },
};

const GroupChart1 = ({dashboard}) => {
  const { t } = useTranslation();
  return (
    <>
    {/* Payment Collection Block */}
    <div className={`py-[18px] px-4 rounded-[6px] bg-[#deffd3] dark:bg-slate-900`} >
          <div className="flex items-center space-x-6 rtl:space-x-reverse">
            <div className="flex-none">

            </div>
            <div className="flex-1">
              <div className="text-slate-800 dark:text-slate-300 text-sm mb-1 font-medium">
                {t("dashboard.total_collection")}
              </div>
              <div className="text-slate-900 dark:text-white text-lg font-medium">
                {dashboard?.total_collection} BDT
              </div>
            </div>
          </div>
        </div>
    {/* Students Block */}
        <div className={`py-[18px] px-4 rounded-[6px] bg-[#E5F9FF] dark:bg-slate-900`} >
          <div className="flex items-center space-x-6 rtl:space-x-reverse">
            <div className="flex-none">

            </div>
            <div className="flex-1">
              <div className="text-slate-800 dark:text-slate-300 text-sm mb-1 font-medium">
                {t("dashboard.total_students")}
              </div>
              <div className="text-slate-900 dark:text-white text-lg font-medium">
                {dashboard?.students_count}
              </div>
            </div>
          </div>
        </div>

    {/* Mentors Block */}
        <div className={`py-[18px] px-4 rounded-[6px] bg-[#FFEDE5] dark:bg-slate-900`} >
          <div className="flex items-center space-x-6 rtl:space-x-reverse">
            <div className="flex-none">

            </div>
            <div className="flex-1">
              <div className="text-slate-800 dark:text-slate-300 text-sm mb-1 font-medium">
                {t("dashboard.mentor_number")}
              </div>
              <div className="text-slate-900 dark:text-white text-lg font-medium">
                {dashboard?.mentors_count}
              </div>
            </div>
          </div>
        </div>

    {/* Course Block */}
        <div className={`py-[18px] px-4 rounded-[6px] bg-[#EAE5FF] dark:bg-slate-900`} >
          <div className="flex items-center space-x-6 rtl:space-x-reverse">
            <div className="flex-none">

            </div>
            <div className="flex-1">
              <div className="text-slate-800 dark:text-slate-300 text-sm mb-1 font-medium">
                {t("dashboard.total_available_courses")}
              </div>
              <div className="text-slate-900 dark:text-white text-lg font-medium">
                {dashboard?.courses_count}
              </div>
            </div>
          </div>
        </div>


    </>
  );
};

export default GroupChart1;
