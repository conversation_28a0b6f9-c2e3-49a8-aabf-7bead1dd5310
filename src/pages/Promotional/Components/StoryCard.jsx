import { useTranslation } from 'react-i18next';

const StoryCard = ({ story }) => {
  const { t } = useTranslation();
  return (
    <div className="story-card bg-gray-100 shadow-[2px_4px_4px_0px_#CBD5E1] hover:shadow-[6px_10px_0px_0px_#E0E7FF] p-6 border border-transparent hover:border-indigo-400 rounded-lg transition-all duration-200">
      <div className="flex gap-4">
        <img src={story.image} alt={story.title} className="w-14 h-14 rounded-full border object-cover" />
        <div>
          <h4 className="font-semibold text-xl">{t(story.nameKey) || story.name}</h4>
          <p>{t(story.positionKey) || story.position}</p>
        </div>
      </div>
      <p className="mt-2">{t(story.descriptionKey) || (story.description.length > 100 ? `${story.description.slice(0, 100)}...` : story.description)}</p>
    </div>
  );
};



export default StoryCard;
